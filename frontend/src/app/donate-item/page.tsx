'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import DonationForm from '@/components/donation/DonationForm';
import { toast } from 'sonner';

interface DonationFormData {
  title: string;
  description: string;
  category: string;
  subcategory: string;
  brand: string;
  size: string;
  color: string;
  condition: string;
  tags: string;
  location: {
    county: string;
    town: string;
  };
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo: string;
  };
}

export default function DonateItemPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (formData: DonationFormData, images: File[]) => {
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to donate an item');
      }

      // Create FormData for file upload
      const submitData = new FormData();
      
      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'sustainabilityInfo') {
          Object.entries(value as Record<string, any>).forEach(([subKey, subValue]) => {
            submitData.append(`sustainabilityInfo.${subKey}`, String(subValue));
          });
        } else if (key === 'location') {
          Object.entries(value as Record<string, any>).forEach(([subKey, subValue]) => {
            submitData.append(`location.${subKey}`, String(subValue));
          });
        } else {
          submitData.append(key, String(value));
        }
      });

      // Set exchange type to donation
      submitData.append('exchangeType', 'donation');

      // Add images
      images.forEach((image, index) => {
        submitData.append('images', image);
      });

      const response = await fetch('/api/clothing', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: submitData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to list item for donation');
      }

      setSuccess(true);
      toast.success('Item listed for donation successfully! You\'ve earned tokens for your generosity.');
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);

    } catch (error) {
      console.error('Error listing item for donation:', error);
      setError(error instanceof Error ? error.message : 'Failed to list item for donation');
      toast.error('Failed to list item for donation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8 bg-white rounded-lg shadow-lg">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Donation Listed Successfully!</h2>
          <p className="text-gray-600 mb-4">
            Thank you for your generosity! Your item has been listed for donation and you've earned tokens.
          </p>
          <p className="text-sm text-gray-500">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Donate Clothing Item</h1>
          <p className="text-gray-600">
            List your clothing item for donation to help others in need while earning tokens for your kindness.
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <DonationForm onSubmit={handleSubmit} loading={loading} />
      </div>
    </div>
  );
}
